# Generated by Django 5.2.6 on 2025-09-25 06:31

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='FaceDetectionSession',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('session_key', models.CharField(max_length=100, unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('last_activity', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-last_activity'],
            },
        ),
        migrations.CreateModel(
            name='FaceDetectionResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(blank=True, null=True, upload_to='face_images/%Y/%m/%d/')),
                ('landmarks_data', models.JSONField()),
                ('confidence_score', models.FloatField()),
                ('face_width', models.FloatField()),
                ('face_height', models.FloatField()),
                ('face_angle', models.FloatField()),
                ('processing_time', models.FloatField(help_text='Processing time in milliseconds')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='detections', to='face_detection.facedetectionsession')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='AccessoryTryOn',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('accessory_type', models.CharField(choices=[('glasses', 'Glasses'), ('hat', 'Hat'), ('earrings', 'Earrings'), ('necklace', 'Necklace')], max_length=20)),
                ('accessory_model_id', models.PositiveIntegerField()),
                ('position_adjustments', models.JSONField(default=dict)),
                ('scale_factor', models.FloatField(default=1.0)),
                ('rotation_adjustments', models.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='face_detection.facedetectionsession')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='facedetectionsession',
            index=models.Index(fields=['session_key'], name='face_detect_session_f866fb_idx'),
        ),
        migrations.AddIndex(
            model_name='facedetectionsession',
            index=models.Index(fields=['created_at'], name='face_detect_created_307b40_idx'),
        ),
        migrations.AddIndex(
            model_name='facedetectionresult',
            index=models.Index(fields=['session', '-created_at'], name='face_detect_session_f880ed_idx'),
        ),
        migrations.AddIndex(
            model_name='facedetectionresult',
            index=models.Index(fields=['confidence_score'], name='face_detect_confide_27fd07_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='accessorytryon',
            unique_together={('session', 'accessory_type')},
        ),
    ]
