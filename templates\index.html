{% extends 'base.html' %}
{% load static %}

{% block extra_css %}
<style>
    .hero-section {
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .hero-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }

    .hero-content {
        position: relative;
        z-index: 1;
        max-width: 800px;
        padding: 2rem;
    }

    .hero-title {
        font-size: 4rem;
        font-weight: 800;
        margin-bottom: 1rem;
        background: linear-gradient(45deg, #fff, #f0f0f0);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }

    .hero-subtitle {
        font-size: 1.5rem;
        margin-bottom: 2rem;
        opacity: 0.9;
        font-weight: 300;
    }

    .hero-description {
        font-size: 1.1rem;
        margin-bottom: 3rem;
        opacity: 0.8;
        line-height: 1.6;
    }

    .cta-button {
        display: inline-flex;
        align-items: center;
        gap: 1rem;
        padding: 1.5rem 3rem;
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        text-decoration: none;
        border-radius: 50px;
        font-size: 1.2rem;
        font-weight: 600;
        box-shadow: 0 15px 35px rgba(31, 38, 135, 0.5);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .cta-button:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(31, 38, 135, 0.6);
        color: white;
        text-decoration: none;
    }

    .cta-button i {
        font-size: 1.5rem;
    }

    .features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        margin-top: 4rem;
    }

    .feature {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
    }

    .feature:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(31, 38, 135, 0.3);
    }

    .feature i {
        font-size: 3rem;
        color: #f093fb;
        margin-bottom: 1rem;
    }

    .feature h3 {
        font-size: 1.3rem;
        margin-bottom: 1rem;
        font-weight: 600;
    }

    .feature p {
        opacity: 0.8;
        line-height: 1.5;
    }

    @media (max-width: 768px) {
        .hero-title {
            font-size: 2.5rem;
        }

        .hero-subtitle {
            font-size: 1.2rem;
        }

        .cta-button {
            padding: 1.2rem 2rem;
            font-size: 1.1rem;
        }

        .features {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="hero-section">
    <div class="hero-content">
        <h1 class="hero-title">
            <i class="fas fa-magic"></i>
            Virtual Try-On Studio
        </h1>

        <p class="hero-subtitle">
            Experience the future of fashion with AI-powered virtual accessories
        </p>

        <p class="hero-description">
            Try on glasses, hats, and other accessories in real-time using advanced computer vision and 3D rendering technology.
            No downloads required - just open your camera and start exploring!
        </p>

        <a href="{% url 'tryron' %}" class="cta-button">
            <i class="fas fa-video"></i>
            <span>Start Virtual Try-On</span>
        </a>

        <div class="features">
            <div class="feature">
                <i class="fas fa-eye"></i>
                <h3>Real-time Face Detection</h3>
                <p>Advanced AI tracks your facial features with precision for perfect accessory placement</p>
            </div>

            <div class="feature">
                <i class="fas fa-cube"></i>
                <h3>3D Model Support</h3>
                <p>Upload and try on custom 3D models in .glb and .gltf formats</p>
            </div>

            <div class="feature">
                <i class="fas fa-sliders-h"></i>
                <h3>Perfect Adjustments</h3>
                <p>Fine-tune scale, position, and rotation for the perfect fit</p>
            </div>

            <div class="feature">
                <i class="fas fa-camera"></i>
                <h3>Instant Capture</h3>
                <p>Save photos of your virtual try-on sessions with one click</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}