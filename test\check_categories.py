#!/usr/bin/env python3
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'virtual_try_on.settings')
django.setup()

from models_manager.models import AccessoryCategory

# Create categories if they don't exist
glasses_cat, created = AccessoryCategory.objects.get_or_create(
    slug='glasses',
    defaults={
        'name': 'Glasses',
        'description': 'Eyewear and sunglasses',
        'icon': '👓',
        'sort_order': 1
    }
)
if created:
    print(f"Created category: {glasses_cat.name}")

hats_cat, created = AccessoryCategory.objects.get_or_create(
    slug='hats',
    defaults={
        'name': 'Hats',
        'description': 'Hats and headwear',
        'icon': '🎩',
        'sort_order': 2
    }
)
if created:
    print(f"Created category: {hats_cat.name}")

# Print all categories
print("\nAll categories:")
for cat in AccessoryCategory.objects.all():
    print(f"  ID: {cat.id}, Name: {cat.name}, Slug: {cat.slug}")
