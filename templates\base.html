<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Virtual Try-On Booth - AR Experience{% endblock %}</title>
    {% load static %}
    
    <!-- Styles -->
    <link rel="stylesheet" href="{% static 'css/main.css' %}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Three.js and AR Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://threejs.org/examples/js/loaders/GLTFLoader.js"></script>
    
    <!-- MediaPipe -->
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/camera_utils/camera_utils.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/control_utils/control_utils.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/drawing_utils/drawing_utils.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh/face_mesh.js"></script>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <div class="logo">
                <i class="fas fa-mask"></i>
                <span>Virtual Try-On</span>
            </div>
            <ul class="nav-links">
                <li>
                    <a href="{% url 'home' %}">
                        <i class="fas fa-home"></i> Home
                    </a>
                </li>
                <li>
                    <a href="{% url 'tryron' %}">
                        <i class="fas fa-camera"></i> Try On
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer>
        <div class="container">
            <p>&copy; 2025 Virtual Try-On Booth. Powered by AI & AR Technology.</p>
        </div>
    </footer>

    <!-- Core JavaScript -->
    <script src="{% static 'js/core.js' %}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>