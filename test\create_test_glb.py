#!/usr/bin/env python3
"""
Create a minimal test GLB file for upload testing
"""

import struct
import json

def create_minimal_glb():
    """Create a minimal valid GLB file"""
    
    # GLB file structure:
    # Header (12 bytes): magic, version, length
    # JSON chunk header (8 bytes): chunk length, chunk type
    # JSON data (padded to 4-byte boundary)
    # BIN chunk header (8 bytes): chunk length, chunk type  
    # Binary data (padded to 4-byte boundary)
    
    # Minimal glTF JSON
    gltf_json = {
        "asset": {
            "version": "2.0",
            "generator": "Test GLB Creator"
        },
        "scene": 0,
        "scenes": [
            {
                "nodes": [0]
            }
        ],
        "nodes": [
            {
                "mesh": 0
            }
        ],
        "meshes": [
            {
                "primitives": [
                    {
                        "attributes": {
                            "POSITION": 0
                        },
                        "indices": 1
                    }
                ]
            }
        ],
        "accessors": [
            {
                "bufferView": 0,
                "componentType": 5126,  # FLOAT
                "count": 3,
                "type": "VEC3",
                "max": [1.0, 1.0, 0.0],
                "min": [-1.0, -1.0, 0.0]
            },
            {
                "bufferView": 1,
                "componentType": 5123,  # UNSIGNED_SHORT
                "count": 3,
                "type": "SCALAR"
            }
        ],
        "bufferViews": [
            {
                "buffer": 0,
                "byteOffset": 0,
                "byteLength": 36
            },
            {
                "buffer": 0,
                "byteOffset": 36,
                "byteLength": 6
            }
        ],
        "buffers": [
            {
                "byteLength": 42
            }
        ]
    }
    
    # Convert JSON to bytes
    json_str = json.dumps(gltf_json, separators=(',', ':'))
    json_bytes = json_str.encode('utf-8')
    
    # Pad JSON to 4-byte boundary
    json_padding = (4 - (len(json_bytes) % 4)) % 4
    json_bytes += b' ' * json_padding
    
    # Binary data: simple triangle
    # 3 vertices (x, y, z) as floats
    vertices = struct.pack('<9f', 
        -1.0, -1.0, 0.0,  # vertex 0
         1.0, -1.0, 0.0,  # vertex 1
         0.0,  1.0, 0.0   # vertex 2
    )
    
    # 3 indices as unsigned shorts
    indices = struct.pack('<3H', 0, 1, 2)
    
    binary_data = vertices + indices
    
    # Pad binary data to 4-byte boundary
    bin_padding = (4 - (len(binary_data) % 4)) % 4
    binary_data += b'\x00' * bin_padding
    
    # GLB header
    magic = b'glTF'
    version = struct.pack('<I', 2)
    
    # Calculate total length
    header_length = 12
    json_chunk_header_length = 8
    bin_chunk_header_length = 8
    total_length = (header_length + 
                   json_chunk_header_length + len(json_bytes) +
                   bin_chunk_header_length + len(binary_data))
    
    length = struct.pack('<I', total_length)
    
    # JSON chunk header
    json_chunk_length = struct.pack('<I', len(json_bytes))
    json_chunk_type = b'JSON'
    
    # BIN chunk header
    bin_chunk_length = struct.pack('<I', len(binary_data))
    bin_chunk_type = b'BIN\x00'
    
    # Assemble GLB file
    glb_data = (magic + version + length +
                json_chunk_length + json_chunk_type + json_bytes +
                bin_chunk_length + bin_chunk_type + binary_data)
    
    return glb_data

def main():
    """Create test GLB files"""
    
    # Create minimal GLB
    glb_data = create_minimal_glb()
    
    # Save to file
    with open('test_glasses.glb', 'wb') as f:
        f.write(glb_data)
    
    print(f"Created test_glasses.glb ({len(glb_data)} bytes)")
    
    # Create another test file
    with open('test_hat.glb', 'wb') as f:
        f.write(glb_data)
    
    print(f"Created test_hat.glb ({len(glb_data)} bytes)")
    
    print("\nTest GLB files created successfully!")
    print("You can now use these files to test the upload functionality.")

if __name__ == "__main__":
    main()
