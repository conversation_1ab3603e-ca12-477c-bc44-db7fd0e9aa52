# Generated by Django 5.2.6 on 2025-09-25 05:55

import django.core.validators
import django.db.models.deletion
import models_manager.models
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AccessoryCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True)),
                ('slug', models.SlugField(unique=True)),
                ('description', models.TextField(blank=True)),
                ('icon', models.CharField(blank=True, help_text='Font Awesome icon class', max_length=50)),
                ('sort_order', models.PositiveIntegerField(default=0)),
                ('is_active', models.Bo<PERSON>an<PERSON>ield(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Accessory Categories',
                'ordering': ['sort_order', 'name'],
                'indexes': [models.Index(fields=['slug'], name='models_mana_slug_2dfbd2_idx'), models.Index(fields=['is_active'], name='models_mana_is_acti_6800ac_idx')],
            },
        ),
        migrations.CreateModel(
            name='AccessoryModel',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('model_file', models.FileField(upload_to=models_manager.models.model_upload_path, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['glb', 'gltf'])])),
                ('thumbnail', models.ImageField(blank=True, upload_to=models_manager.models.thumbnail_upload_path)),
                ('quality', models.CharField(choices=[('low', 'Low (< 1MB)'), ('medium', 'Medium (1-5MB)'), ('high', 'High (5-10MB)'), ('ultra', 'Ultra (> 10MB)')], default='medium', max_length=10)),
                ('file_size', models.PositiveIntegerField(blank=True, help_text='File size in bytes', null=True)),
                ('polygon_count', models.PositiveIntegerField(blank=True, null=True)),
                ('default_scale', models.FloatField(default=1.0, validators=[django.core.validators.MinValueValidator(0.1), django.core.validators.MaxValueValidator(5.0)])),
                ('default_position_x', models.FloatField(default=0.0)),
                ('default_position_y', models.FloatField(default=0.0)),
                ('default_position_z', models.FloatField(default=0.0)),
                ('default_rotation_x', models.FloatField(default=0.0)),
                ('default_rotation_y', models.FloatField(default=0.0)),
                ('default_rotation_z', models.FloatField(default=0.0)),
                ('tags', models.JSONField(blank=True, default=list)),
                ('is_active', models.BooleanField(default=True)),
                ('is_featured', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('download_count', models.PositiveIntegerField(default=0)),
                ('usage_count', models.PositiveIntegerField(default=0)),
                ('average_rating', models.FloatField(default=0.0)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='models', to='models_manager.accessorycategory')),
                ('upload_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-is_featured', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ModelCollection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('is_public', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('models', models.ManyToManyField(blank=True, to='models_manager.accessorymodel')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ModelRating',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_key', models.CharField(blank=True, max_length=100)),
                ('rating', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('comment', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('model', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ratings', to='models_manager.accessorymodel')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddIndex(
            model_name='accessorymodel',
            index=models.Index(fields=['category', 'is_active'], name='models_mana_categor_fde2b4_idx'),
        ),
        migrations.AddIndex(
            model_name='accessorymodel',
            index=models.Index(fields=['is_featured'], name='models_mana_is_feat_b3f10f_idx'),
        ),
        migrations.AddIndex(
            model_name='accessorymodel',
            index=models.Index(fields=['quality'], name='models_mana_quality_25b85b_idx'),
        ),
        migrations.AddIndex(
            model_name='modelrating',
            index=models.Index(fields=['model', 'rating'], name='models_mana_model_i_76a6f9_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='modelrating',
            unique_together={('model', 'user', 'session_key')},
        ),
    ]
