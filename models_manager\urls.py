from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from . import views

router = DefaultRouter()
router.register('categories', views.AccessoryCategoryViewSet)
router.register('models', views.AccessoryModelViewSet, basename='accessorymodel')
router.register('collections', views.ModelCollectionViewSet, basename='modelcollection')

app_name = 'models_manager'

urlpatterns = [
    path('', include(router.urls)),
    path('list/', views.list_models_by_category, name='list_models'),
]